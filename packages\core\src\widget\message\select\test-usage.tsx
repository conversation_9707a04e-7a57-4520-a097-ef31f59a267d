/**
 * SelectInteractiveWidget 测试用例
 * 用于验证新增的接口请求功能
 */

import React from "react";
import SelectInteractiveWidget, { ApiConfig } from "./index";

// 模拟接口配置
const mockApiConfigs: Record<string, ApiConfig> = {
  // GET 请求示例
  getExample: {
    url: "/api/test/options",
    method: "GET",
    headers: {
      "Authorization": "Bearer test-token"
    },
    params: {
      category: "test",
      limit: 10
    }
  },

  // POST 请求示例
  postExample: {
    url: "/api/test/search",
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: {
      query: "test",
      filters: {
        active: true
      }
    }
  },

  // PUT 请求示例
  putExample: {
    url: "/api/test/update",
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "X-Custom": "value"
    },
    data: {
      action: "fetch_options"
    }
  }
};

/**
 * 测试组件1：基本功能测试
 */
export const BasicFunctionalityTest = () => {
  return (
    <div style={{ padding: "20px", display: "flex", flexDirection: "column", gap: "20px" }}>
      <h3>基本功能测试</h3>
      
      {/* 仅静态数据 */}
      <div>
        <h4>1. 仅静态数据</h4>
        <SelectInteractiveWidget
          options={[
            { label: "静态选项1", value: "static1" },
            { label: "静态选项2", value: "static2" }
          ]}
          placeholder="请选择静态选项"
        />
      </div>

      {/* 仅接口数据 */}
      <div>
        <h4>2. 仅接口数据 (GET)</h4>
        <SelectInteractiveWidget
          apiConfig={mockApiConfigs.getExample}
          placeholder="请选择接口选项"
        />
      </div>

      {/* 混合数据 */}
      <div>
        <h4>3. 混合数据</h4>
        <SelectInteractiveWidget
          options={[
            { label: "静态选项", value: "static" }
          ]}
          apiConfig={mockApiConfigs.postExample}
          placeholder="请选择混合选项"
        />
      </div>
    </div>
  );
};

/**
 * 测试组件2：不同HTTP方法测试
 */
export const HttpMethodsTest = () => {
  return (
    <div style={{ padding: "20px", display: "flex", flexDirection: "column", gap: "20px" }}>
      <h3>HTTP 方法测试</h3>
      
      <div>
        <h4>GET 请求</h4>
        <SelectInteractiveWidget
          apiConfig={mockApiConfigs.getExample}
          placeholder="GET 请求测试"
        />
      </div>

      <div>
        <h4>POST 请求</h4>
        <SelectInteractiveWidget
          apiConfig={mockApiConfigs.postExample}
          placeholder="POST 请求测试"
        />
      </div>

      <div>
        <h4>PUT 请求</h4>
        <SelectInteractiveWidget
          apiConfig={mockApiConfigs.putExample}
          placeholder="PUT 请求测试"
        />
      </div>
    </div>
  );
};

/**
 * 测试组件3：错误处理测试
 */
export const ErrorHandlingTest = () => {
  const errorApiConfig: ApiConfig = {
    url: "/api/non-existent-endpoint",
    method: "GET"
  };

  return (
    <div style={{ padding: "20px", display: "flex", flexDirection: "column", gap: "20px" }}>
      <h3>错误处理测试</h3>
      
      <div>
        <h4>接口错误 + 静态备用数据</h4>
        <SelectInteractiveWidget
          options={[
            { label: "备用选项1", value: "fallback1" },
            { label: "备用选项2", value: "fallback2" }
          ]}
          apiConfig={errorApiConfig}
          placeholder="错误处理测试"
        />
      </div>
    </div>
  );
};

/**
 * 测试组件4：动态配置测试
 */
export const DynamicConfigTest = () => {
  const [currentConfig, setCurrentConfig] = React.useState<ApiConfig | undefined>(
    mockApiConfigs.getExample
  );

  const switchConfig = (configKey: string) => {
    setCurrentConfig(mockApiConfigs[configKey]);
  };

  return (
    <div style={{ padding: "20px", display: "flex", flexDirection: "column", gap: "20px" }}>
      <h3>动态配置测试</h3>
      
      <div style={{ display: "flex", gap: "10px", marginBottom: "10px" }}>
        <button onClick={() => switchConfig("getExample")}>切换到 GET</button>
        <button onClick={() => switchConfig("postExample")}>切换到 POST</button>
        <button onClick={() => switchConfig("putExample")}>切换到 PUT</button>
        <button onClick={() => setCurrentConfig(undefined)}>清除配置</button>
      </div>

      <SelectInteractiveWidget
        options={[
          { label: "固定选项", value: "fixed" }
        ]}
        apiConfig={currentConfig}
        placeholder="动态配置测试"
      />
    </div>
  );
};

/**
 * 完整测试套件
 */
export const TestSuite = () => {
  return (
    <div>
      <h2>SelectInteractiveWidget 测试套件</h2>
      <BasicFunctionalityTest />
      <HttpMethodsTest />
      <ErrorHandlingTest />
      <DynamicConfigTest />
    </div>
  );
};

export default TestSuite;
